# 🎓 Student Registration System

A fully containerized Student Registration System built with Django, featuring real-time data streaming, analytics dashboard, and comprehensive API endpoints.

## 🏗️ Architecture

The system consists of the following components:

- **Django Backend**: REST API with student, course, and registration management
- **MySQL Database**: Primary data storage with binary logging enabled
- **Maxwell Daemon**: Real-time MySQL binlog streaming
- **Streamlit Dashboard**: Interactive analytics and monitoring interface
- **Nginx**: Reverse proxy and static file server
- **Docker**: Containerization and orchestration

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 4GB RAM available
- Ports 80, 443, 3306, 8000, 8501 available

### 1. Clone and Setup

```bash
git clone <repository-url>
cd student-registration-system
```

### 2. Environment Configuration

Copy and modify the environment file:

```bash
cp .env.example .env
# Edit .env with your preferred settings
```

### 3. Start the System

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Access the System

- **Main Dashboard**: http://localhost/dashboard/
- **Django Admin**: http://localhost/admin/
- **API Documentation**: http://localhost/accounts/api/
- **Streamlit Dashboard**: http://localhost:8501/

## 📊 System Components

### Django Backend

The Django application provides:

- **Student Management**: CRUD operations for student records
- **Course Management**: Course creation and enrollment tracking
- **Registration System**: Student course registration with automatic enrollment/waitlisting
- **REST API**: Comprehensive API endpoints with filtering, search, and pagination
- **Admin Interface**: Django admin for system management

#### Key Models

- `Student`: Student information and profile data
- `Course`: Course details, capacity, and scheduling
- `Registration`: Student-course enrollment relationships

#### API Endpoints

```
GET/POST   /accounts/api/students/           # List/Create students
GET/PUT    /accounts/api/students/{id}/      # Student details
GET/POST   /accounts/api/courses/            # List/Create courses
GET/PUT    /accounts/api/courses/{code}/     # Course details
GET/POST   /accounts/api/registrations/      # List/Create registrations
GET        /accounts/api/dashboard/stats/    # Dashboard statistics
GET        /accounts/api/dashboard/trends/   # Registration trends
```

### MySQL Database

Configured with:

- Binary logging enabled for real-time streaming
- Optimized for read/write performance
- Automatic backups (via Docker volumes)
- User management for different service access levels

### Maxwell Daemon

Real-time MySQL binlog streaming:

- Captures all database changes in real-time
- Configurable output formats (JSON, Kafka)
- Filtering for specific tables/operations
- Monitoring and metrics collection

### Streamlit Dashboard

Interactive analytics interface featuring:

- **Overview**: Key metrics and enrollment statistics
- **Student Management**: Student data visualization and management
- **Course Analytics**: Course enrollment and capacity tracking
- **Registration Monitoring**: Real-time registration status tracking
- **Real-time Events**: Live database change monitoring
- **Auto-refresh**: Configurable real-time data updates

### Nginx Reverse Proxy

- **Load Balancing**: Distributes traffic across services
- **Static File Serving**: Efficient static and media file delivery
- **SSL Termination**: HTTPS support with configurable certificates
- **Rate Limiting**: API protection and abuse prevention
- **Caching**: Optimized response caching

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Django Settings
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=localhost,yourdomain.com

# Database
DB_NAME=student_registration
DB_USER=root
DB_PASSWORD=secure-password
DB_HOST=mysql
DB_PORT=3306

# Admin User
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=secure-admin-password
```

### Service Configuration

Each service can be configured through:

- Environment variables
- Configuration files in respective directories
- Docker Compose overrides

## 📈 Monitoring and Analytics

### Real-time Monitoring

The system provides multiple monitoring capabilities:

1. **Maxwell Event Streaming**: Real-time database change tracking
2. **Streamlit Dashboard**: Live analytics and metrics
3. **Nginx Access Logs**: Request monitoring and analysis
4. **Docker Health Checks**: Service availability monitoring

### Key Metrics

- Total students, courses, and registrations
- Course enrollment percentages and availability
- Registration trends and patterns
- System performance and uptime

## 🔒 Security Features

- **Environment-based Configuration**: Sensitive data in environment variables
- **Rate Limiting**: API protection against abuse
- **CORS Configuration**: Controlled cross-origin access
- **SSL Support**: HTTPS encryption capability
- **User Authentication**: Django's built-in authentication system
- **Input Validation**: Comprehensive data validation

## 🚀 Deployment

### Production Deployment

1. **Update Environment Variables**:
   ```bash
   # Set production values
   DEBUG=False
   SECRET_KEY=production-secret-key
   ALLOWED_HOSTS=yourdomain.com
   ```

2. **SSL Configuration**:
   ```bash
   # Generate SSL certificates
   ./nginx/ssl/generate-ssl.sh
   
   # Or use Let's Encrypt
   # Configure in nginx/conf.d/ssl.conf
   ```

3. **Database Backup**:
   ```bash
   # Setup automated backups
   docker-compose exec mysql mysqldump -u root -p student_registration > backup.sql
   ```

4. **Monitoring Setup**:
   ```bash
   # Enable production monitoring
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

### Scaling

The system supports horizontal scaling:

- **Database**: MySQL replication and clustering
- **Application**: Multiple Django instances behind Nginx
- **Analytics**: Distributed Streamlit instances
- **Storage**: Shared volumes for static/media files

## 🧪 Testing

### Running Tests

```bash
# Django tests
docker-compose exec django python manage.py test

# API endpoint testing
curl -X GET http://localhost/accounts/api/students/

# Health checks
docker-compose exec nginx wget -qO- http://localhost/health
```

### Sample Data

Load sample data for testing:

```bash
# Create sample students and courses
docker-compose exec django python manage.py shell < scripts/load_sample_data.py
```

## 📚 API Documentation

### Authentication

The API supports multiple authentication methods:

- Session authentication (for web interface)
- Token authentication (for API clients)
- Basic authentication (for development)

### Response Format

All API responses follow a consistent format:

```json
{
  "count": 100,
  "next": "http://localhost/accounts/api/students/?page=2",
  "previous": null,
  "results": [...]
}
```

### Error Handling

Standardized error responses:

```json
{
  "error": "Validation failed",
  "details": {
    "student_id": ["This field is required."]
  }
}
```

## 🔧 Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check port usage
   netstat -tulpn | grep :80
   
   # Modify ports in docker-compose.yml
   ```

2. **Database Connection Issues**:
   ```bash
   # Check MySQL status
   docker-compose logs mysql
   
   # Verify network connectivity
   docker-compose exec django nc -z mysql 3306
   ```

3. **Maxwell Not Streaming**:
   ```bash
   # Check Maxwell logs
   docker-compose logs maxwell
   
   # Verify binlog configuration
   docker-compose exec mysql mysql -u root -p -e "SHOW VARIABLES LIKE 'log_bin';"
   ```

### Performance Optimization

- **Database Indexing**: Ensure proper indexes on frequently queried fields
- **Nginx Caching**: Configure appropriate cache headers
- **Static File Optimization**: Use CDN for static assets in production
- **Database Connection Pooling**: Configure Django database pooling

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review Docker Compose logs
3. Consult the API documentation
4. Create an issue in the repository

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
