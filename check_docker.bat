@echo off
echo Checking Docker Desktop status...
echo.

REM Check if Docker Desktop process is running
tasklist /FI "IMAGENAME eq Docker Desktop.exe" 2>NUL | find /I /N "Docker Desktop.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [INFO] Docker Desktop process is running
) else (
    echo [ERROR] Docker Desktop process is NOT running
    echo.
    echo Please start Docker Desktop:
    echo 1. Press Windows key + R
    echo 2. Type: "Docker Desktop"
    echo 3. Press Enter
    echo 4. Wait for Docker to fully start
    echo.
    pause
    exit /b 1
)

REM Check if Docker engine is accessible
echo [INFO] Testing Docker engine connection...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker engine is not accessible
    echo.
    echo Docker Desktop might still be starting up.
    echo Please wait a few more minutes and try again.
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] Docker engine is running and accessible!
    echo.
    echo You can now run: docker-compose up -d
    echo.
)

pause
