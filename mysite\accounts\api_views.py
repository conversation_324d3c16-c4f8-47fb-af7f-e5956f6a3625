from django.shortcuts import render
from rest_framework import generics, status, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from .models import Student, Course, Registration
from .serializers import (
    StudentSerializer, StudentCreateSerializer,
    CourseSerializer, RegistrationSerializer, RegistrationCreateSerializer
)


# Student Views
class StudentListCreateView(generics.ListCreateAPIView):
    queryset = Student.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['year_of_study', 'major', 'is_active']
    search_fields = ['student_id', 'user__first_name', 'user__last_name', 'user__email']
    ordering_fields = ['student_id', 'created_at']
    ordering = ['student_id']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return StudentCreateSerializer
        return StudentSerializer


class StudentDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Student.objects.all()
    serializer_class = StudentSerializer
    lookup_field = 'student_id'


# Course Views
class CourseListCreateView(generics.ListCreateAPIView):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['semester', 'year', 'is_active', 'instructor']
    search_fields = ['course_code', 'course_name', 'instructor']
    ordering_fields = ['course_code', 'course_name', 'created_at']
    ordering = ['course_code']


class CourseDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    lookup_field = 'course_code'


# Registration Views
class RegistrationListCreateView(generics.ListCreateAPIView):
    queryset = Registration.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'course__semester', 'course__year']
    search_fields = ['student__student_id', 'course__course_code', 'student__user__first_name', 'student__user__last_name']
    ordering_fields = ['registration_date', 'status']
    ordering = ['-registration_date']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RegistrationCreateSerializer
        return RegistrationSerializer


class RegistrationDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Registration.objects.all()
    serializer_class = RegistrationSerializer


class StudentRegistrationsView(generics.ListAPIView):
    serializer_class = RegistrationSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'course__semester', 'course__year']
    ordering_fields = ['registration_date', 'status']
    ordering = ['-registration_date']

    def get_queryset(self):
        student_id = self.kwargs['student_id']
        return Registration.objects.filter(student__student_id=student_id)


class CourseRegistrationsView(generics.ListAPIView):
    serializer_class = RegistrationSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['registration_date', 'student__student_id']
    ordering = ['student__student_id']

    def get_queryset(self):
        course_code = self.kwargs['course_code']
        return Registration.objects.filter(course__course_code=course_code)


# Dashboard API Views
@api_view(['GET'])
def dashboard_stats(request):
    """Get dashboard statistics"""
    total_students = Student.objects.filter(is_active=True).count()
    total_courses = Course.objects.filter(is_active=True).count()
    total_registrations = Registration.objects.filter(status='ENROLLED').count()
    pending_registrations = Registration.objects.filter(status='PENDING').count()
    waitlisted_registrations = Registration.objects.filter(status='WAITLISTED').count()

    # Course enrollment stats
    course_stats = []
    for course in Course.objects.filter(is_active=True):
        course_stats.append({
            'course_code': course.course_code,
            'course_name': course.course_name,
            'current_enrollment': course.current_enrollment,
            'max_students': course.max_students,
            'available_spots': course.available_spots,
            'enrollment_percentage': round((course.current_enrollment / course.max_students) * 100, 2)
        })

    return Response({
        'total_students': total_students,
        'total_courses': total_courses,
        'total_registrations': total_registrations,
        'pending_registrations': pending_registrations,
        'waitlisted_registrations': waitlisted_registrations,
        'course_stats': course_stats
    })


@api_view(['GET'])
def registration_trends(request):
    """Get registration trends data"""
    from django.db.models import Count
    from datetime import datetime, timedelta

    # Registration trends by day for the last 30 days
    thirty_days_ago = datetime.now() - timedelta(days=30)
    daily_registrations = Registration.objects.filter(
        registration_date__gte=thirty_days_ago
    ).extra(
        select={'day': 'date(registration_date)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')

    # Registration status distribution
    status_distribution = Registration.objects.values('status').annotate(
        count=Count('id')
    ).order_by('status')

    return Response({
        'daily_registrations': list(daily_registrations),
        'status_distribution': list(status_distribution)
    })
