from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Student, Course, Registration


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class StudentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    full_name = serializers.ReadOnlyField()
    email = serializers.ReadOnlyField()

    class Meta:
        model = Student
        fields = [
            'id', 'user', 'student_id', 'phone_number', 'date_of_birth',
            'address', 'emergency_contact_name', 'emergency_contact_phone',
            'major', 'year_of_study', 'gpa', 'is_active', 'full_name', 'email',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class StudentCreateSerializer(serializers.ModelSerializer):
    username = serializers.<PERSON><PERSON><PERSON><PERSON>(write_only=True)
    email = serializers.EmailField(write_only=True)
    first_name = serializers.CharField(write_only=True)
    last_name = serializers.Char<PERSON>ield(write_only=True)
    password = serializers.CharField(write_only=True, min_length=8)

    class Meta:
        model = Student
        fields = [
            'username', 'email', 'first_name', 'last_name', 'password',
            'student_id', 'phone_number', 'date_of_birth', 'address',
            'emergency_contact_name', 'emergency_contact_phone', 'major', 'year_of_study'
        ]

    def create(self, validated_data):
        # Extract user data
        user_data = {
            'username': validated_data.pop('username'),
            'email': validated_data.pop('email'),
            'first_name': validated_data.pop('first_name'),
            'last_name': validated_data.pop('last_name'),
            'password': validated_data.pop('password'),
        }
        
        # Create user
        user = User.objects.create_user(**user_data)
        
        # Create student
        student = Student.objects.create(user=user, **validated_data)
        return student


class CourseSerializer(serializers.ModelSerializer):
    current_enrollment = serializers.ReadOnlyField()
    available_spots = serializers.ReadOnlyField()

    class Meta:
        model = Course
        fields = [
            'id', 'course_code', 'course_name', 'description', 'credits',
            'max_students', 'instructor', 'semester', 'year', 'is_active',
            'current_enrollment', 'available_spots', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class RegistrationSerializer(serializers.ModelSerializer):
    student_details = StudentSerializer(source='student', read_only=True)
    course_details = CourseSerializer(source='course', read_only=True)

    class Meta:
        model = Registration
        fields = [
            'id', 'student', 'course', 'status', 'registration_date',
            'grade', 'notes', 'student_details', 'course_details',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'registration_date', 'created_at', 'updated_at']

    def validate(self, data):
        student = data.get('student')
        course = data.get('course')
        
        # Check if student is already registered for this course
        if Registration.objects.filter(student=student, course=course).exists():
            raise serializers.ValidationError(
                "Student is already registered for this course."
            )
        
        # Check if course is active
        if not course.is_active:
            raise serializers.ValidationError(
                "Cannot register for inactive course."
            )
        
        return data


class RegistrationCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Registration
        fields = ['student', 'course', 'notes']

    def validate(self, data):
        student = data.get('student')
        course = data.get('course')
        
        # Check if student is already registered for this course
        if Registration.objects.filter(student=student, course=course).exists():
            raise serializers.ValidationError(
                "Student is already registered for this course."
            )
        
        # Check if course is active
        if not course.is_active:
            raise serializers.ValidationError(
                "Cannot register for inactive course."
            )
        
        return data
