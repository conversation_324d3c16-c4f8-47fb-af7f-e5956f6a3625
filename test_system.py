#!/usr/bin/env python3
"""
System Testing Script for Student Registration System
Tests all major components and endpoints
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost"
API_BASE = f"{BASE_URL}/accounts/api"
STREAMLIT_URL = "http://localhost:8501"

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    END = '\033[0m'

def print_status(message):
    print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")

def print_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")

def print_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.END} {message}")

def print_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")

def test_service_health():
    """Test if all services are running and healthy"""
    print_status("Testing service health...")
    
    services = [
        ("Nginx Health Check", f"{BASE_URL}/health"),
        ("Django Admin", f"{BASE_URL}/admin/"),
        ("Streamlit Health", f"{STREAMLIT_URL}/_stcore/health"),
    ]
    
    for service_name, url in services:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print_success(f"{service_name}: OK")
            else:
                print_error(f"{service_name}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print_error(f"{service_name}: Connection failed - {e}")

def test_api_endpoints():
    """Test Django API endpoints"""
    print_status("Testing API endpoints...")
    
    endpoints = [
        ("Students API", f"{API_BASE}/students/"),
        ("Courses API", f"{API_BASE}/courses/"),
        ("Registrations API", f"{API_BASE}/registrations/"),
        ("Dashboard Stats", f"{API_BASE}/dashboard/stats/"),
        ("Dashboard Trends", f"{API_BASE}/dashboard/trends/"),
    ]
    
    for endpoint_name, url in endpoints:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print_success(f"{endpoint_name}: OK (returned {len(str(data))} bytes)")
            else:
                print_error(f"{endpoint_name}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print_error(f"{endpoint_name}: Connection failed - {e}")
        except json.JSONDecodeError:
            print_error(f"{endpoint_name}: Invalid JSON response")

def test_student_crud():
    """Test student CRUD operations"""
    print_status("Testing student CRUD operations...")
    
    # Test data
    student_data = {
        "username": "test.student",
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Student",
        "password": "testpassword123",
        "student_id": "99999999",
        "phone_number": "+1234567890",
        "date_of_birth": "2000-01-01",
        "address": "123 Test St, Test City, TS",
        "emergency_contact_name": "Test Contact",
        "emergency_contact_phone": "+1234567891",
        "major": "Computer Science",
        "year_of_study": "FRESHMAN"
    }
    
    try:
        # Create student
        response = requests.post(f"{API_BASE}/students/", json=student_data)
        if response.status_code == 201:
            print_success("Student creation: OK")
            student_id = response.json().get('student_id')
            
            # Read student
            response = requests.get(f"{API_BASE}/students/{student_id}/")
            if response.status_code == 200:
                print_success("Student retrieval: OK")
                
                # Update student
                update_data = {"major": "Mathematics"}
                response = requests.patch(f"{API_BASE}/students/{student_id}/", json=update_data)
                if response.status_code == 200:
                    print_success("Student update: OK")
                else:
                    print_error(f"Student update failed: HTTP {response.status_code}")
                
                # Delete student (cleanup)
                response = requests.delete(f"{API_BASE}/students/{student_id}/")
                if response.status_code == 204:
                    print_success("Student deletion: OK")
                else:
                    print_warning(f"Student deletion: HTTP {response.status_code}")
            else:
                print_error(f"Student retrieval failed: HTTP {response.status_code}")
        else:
            print_error(f"Student creation failed: HTTP {response.status_code}")
            if response.content:
                print_error(f"Error details: {response.text}")
    except requests.exceptions.RequestException as e:
        print_error(f"Student CRUD test failed: {e}")

def test_course_operations():
    """Test course operations"""
    print_status("Testing course operations...")
    
    course_data = {
        "course_code": "TEST101",
        "course_name": "Test Course",
        "description": "A test course for system testing",
        "credits": 3,
        "max_students": 25,
        "instructor": "Test Instructor",
        "semester": "FALL",
        "year": 2024
    }
    
    try:
        # Create course
        response = requests.post(f"{API_BASE}/courses/", json=course_data)
        if response.status_code == 201:
            print_success("Course creation: OK")
            course_code = response.json().get('course_code')
            
            # Read course
            response = requests.get(f"{API_BASE}/courses/{course_code}/")
            if response.status_code == 200:
                print_success("Course retrieval: OK")
                
                # Cleanup
                response = requests.delete(f"{API_BASE}/courses/{course_code}/")
                if response.status_code == 204:
                    print_success("Course deletion: OK")
                else:
                    print_warning(f"Course deletion: HTTP {response.status_code}")
            else:
                print_error(f"Course retrieval failed: HTTP {response.status_code}")
        else:
            print_error(f"Course creation failed: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print_error(f"Course operations test failed: {e}")

def test_dashboard_data():
    """Test dashboard data endpoints"""
    print_status("Testing dashboard data...")
    
    try:
        # Test stats endpoint
        response = requests.get(f"{API_BASE}/dashboard/stats/")
        if response.status_code == 200:
            stats = response.json()
            print_success(f"Dashboard stats: {stats.get('total_students', 0)} students, {stats.get('total_courses', 0)} courses")
        else:
            print_error(f"Dashboard stats failed: HTTP {response.status_code}")
        
        # Test trends endpoint
        response = requests.get(f"{API_BASE}/dashboard/trends/")
        if response.status_code == 200:
            trends = response.json()
            print_success(f"Dashboard trends: {len(trends.get('daily_registrations', []))} daily records")
        else:
            print_error(f"Dashboard trends failed: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print_error(f"Dashboard data test failed: {e}")

def test_static_files():
    """Test static file serving"""
    print_status("Testing static file serving...")
    
    try:
        response = requests.get(f"{BASE_URL}/static/admin/css/base.css")
        if response.status_code == 200:
            print_success("Static files: OK")
        else:
            print_warning(f"Static files: HTTP {response.status_code}")
    except requests.exceptions.RequestException as e:
        print_warning(f"Static files test: {e}")

def run_performance_test():
    """Run basic performance test"""
    print_status("Running performance test...")
    
    start_time = time.time()
    successful_requests = 0
    total_requests = 10
    
    for i in range(total_requests):
        try:
            response = requests.get(f"{API_BASE}/dashboard/stats/", timeout=5)
            if response.status_code == 200:
                successful_requests += 1
        except:
            pass
    
    end_time = time.time()
    duration = end_time - start_time
    success_rate = (successful_requests / total_requests) * 100
    
    print_success(f"Performance test: {successful_requests}/{total_requests} requests successful ({success_rate:.1f}%)")
    print_success(f"Average response time: {duration/total_requests:.2f} seconds")

def main():
    print("🎓 Student Registration System Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all tests
    test_service_health()
    print()
    
    test_api_endpoints()
    print()
    
    test_student_crud()
    print()
    
    test_course_operations()
    print()
    
    test_dashboard_data()
    print()
    
    test_static_files()
    print()
    
    run_performance_test()
    print()
    
    print("=" * 50)
    print("Test suite completed!")
    print()
    print("If all tests passed, your Student Registration System is working correctly!")
    print("Access the system at:")
    print(f"• Main Dashboard: {BASE_URL}/dashboard/")
    print(f"• Django Admin: {BASE_URL}/admin/")
    print(f"• API Documentation: {API_BASE}/")

if __name__ == "__main__":
    main()
