-- Create <PERSON> user for binlog streaming
CREATE USER IF NOT EXISTS 'maxwell'@'%' IDENTIFIED BY 'maxwell_password';
GRANT ALL PRIVILEGES ON *.* TO 'maxwell'@'%';
GRANT REPLICATION SLAVE ON *.* TO 'maxwell'@'%';
GRANT REPLICATION CLIENT ON *.* TO 'maxwell'@'%';

-- Create Django user
CREATE USER IF NOT EXISTS 'django_user'@'%' IDENTIFIED BY 'django_password';
GRANT ALL PRIVILEGES ON student_registration.* TO 'django_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Enable binary logging (already done in docker-compose command)
-- SET GLOBAL log_bin = ON;
-- SET GLOBAL binlog_format = 'ROW';

-- <PERSON><PERSON> <PERSON> database for metadata
CREATE DATABASE IF NOT EXISTS maxwell;
