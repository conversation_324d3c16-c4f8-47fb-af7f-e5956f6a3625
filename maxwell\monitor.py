#!/usr/bin/env python3
"""
Maxwell Daemon Monitor Script
Monitors <PERSON> output and processes real-time database changes
"""

import json
import sys
import time
import logging
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/maxwell_monitor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class MaxwellMonitor:
    def __init__(self):
        self.stats = {
            'total_events': 0,
            'student_events': 0,
            'course_events': 0,
            'registration_events': 0,
            'start_time': datetime.now()
        }

    def process_event(self, event: Dict[str, Any]) -> None:
        """Process a Maxwell event"""
        try:
            self.stats['total_events'] += 1
            
            table = event.get('table', '')
            event_type = event.get('type', '')
            data = event.get('data', {})
            
            # Log the event
            logger.info(f"Event: {event_type} on {table}")
            
            # Process based on table
            if table == 'accounts_student':
                self.process_student_event(event_type, data)
                self.stats['student_events'] += 1
            elif table == 'accounts_course':
                self.process_course_event(event_type, data)
                self.stats['course_events'] += 1
            elif table == 'accounts_registration':
                self.process_registration_event(event_type, data)
                self.stats['registration_events'] += 1
            
            # Log stats every 100 events
            if self.stats['total_events'] % 100 == 0:
                self.log_stats()
                
        except Exception as e:
            logger.error(f"Error processing event: {e}")

    def process_student_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Process student-related events"""
        if event_type == 'insert':
            logger.info(f"New student registered: {data.get('student_id', 'Unknown')}")
        elif event_type == 'update':
            logger.info(f"Student updated: {data.get('student_id', 'Unknown')}")
        elif event_type == 'delete':
            logger.info(f"Student deleted: {data.get('student_id', 'Unknown')}")

    def process_course_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Process course-related events"""
        if event_type == 'insert':
            logger.info(f"New course created: {data.get('course_code', 'Unknown')}")
        elif event_type == 'update':
            logger.info(f"Course updated: {data.get('course_code', 'Unknown')}")
        elif event_type == 'delete':
            logger.info(f"Course deleted: {data.get('course_code', 'Unknown')}")

    def process_registration_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Process registration-related events"""
        if event_type == 'insert':
            logger.info(f"New registration: Student {data.get('student_id', 'Unknown')} -> Course {data.get('course_id', 'Unknown')}")
        elif event_type == 'update':
            status = data.get('status', 'Unknown')
            logger.info(f"Registration status changed to: {status}")
        elif event_type == 'delete':
            logger.info(f"Registration cancelled")

    def log_stats(self) -> None:
        """Log current statistics"""
        uptime = datetime.now() - self.stats['start_time']
        logger.info(f"Stats - Total: {self.stats['total_events']}, "
                   f"Students: {self.stats['student_events']}, "
                   f"Courses: {self.stats['course_events']}, "
                   f"Registrations: {self.stats['registration_events']}, "
                   f"Uptime: {uptime}")

    def run(self) -> None:
        """Main monitoring loop"""
        logger.info("Starting Maxwell Monitor...")
        
        try:
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    event = json.loads(line)
                    self.process_event(event)
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON: {line[:100]}...")
                    
        except KeyboardInterrupt:
            logger.info("Monitor stopped by user")
        except Exception as e:
            logger.error(f"Monitor error: {e}")
        finally:
            self.log_stats()
            logger.info("Maxwell Monitor stopped")

if __name__ == "__main__":
    monitor = MaxwellMonitor()
    monitor.run()
