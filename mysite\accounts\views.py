from django.shortcuts import render, redirect
from .forms import RegistrationForm

def register(request):
    if request.method == "POST":
        form = RegistrationForm(request.POST)
        if form.is_valid():
            # Dummy save — you'd use User.objects.create_user in real case
            print("User registered:", form.cleaned_data)
            return redirect('/success/')
    else:
        form = RegistrationForm()  # For GET request
    return render(request, "register.html", {"form": form})
