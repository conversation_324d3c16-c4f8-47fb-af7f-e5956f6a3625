import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import time
import json
from datetime import datetime, timedelta

# Configure Streamlit page
st.set_page_config(
    page_title="Student Registration Dashboard",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API Configuration
API_BASE_URL = "http://django:8000/accounts/api"

def fetch_data(endpoint):
    """Fetch data from Django API"""
    try:
        response = requests.get(f"{API_BASE_URL}/{endpoint}")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"Error fetching data from {endpoint}: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {e}")
        return None

def main():
    st.title("🎓 Student Registration Dashboard")
    st.markdown("---")

    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Students", "Courses", "Registrations", "Analytics", "Real-time Monitor"]
    )

    # Auto-refresh settings
    st.sidebar.markdown("---")
    st.sidebar.subheader("⚙️ Settings")
    auto_refresh = st.sidebar.checkbox("Auto-refresh data", value=True)
    refresh_interval = st.sidebar.slider("Refresh interval (seconds)", 5, 60, 10)

    # Real-time refresh
    if auto_refresh:
        placeholder = st.empty()
        with placeholder.container():
            if page == "Overview":
                show_overview()
            elif page == "Students":
                show_students()
            elif page == "Courses":
                show_courses()
            elif page == "Registrations":
                show_registrations()
            elif page == "Analytics":
                show_analytics()
            elif page == "Real-time Monitor":
                show_realtime_monitor()

        time.sleep(refresh_interval)
        st.rerun()
    else:
        if page == "Overview":
            show_overview()
        elif page == "Students":
            show_students()
        elif page == "Courses":
            show_courses()
        elif page == "Registrations":
            show_registrations()
        elif page == "Analytics":
            show_analytics()
        elif page == "Real-time Monitor":
            show_realtime_monitor()

def show_overview():
    st.header("📊 Dashboard Overview")
    
    # Fetch dashboard stats
    stats = fetch_data("dashboard/stats/")
    
    if stats:
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Students", stats.get('total_students', 0))
        
        with col2:
            st.metric("Total Courses", stats.get('total_courses', 0))
        
        with col3:
            st.metric("Active Registrations", stats.get('total_registrations', 0))
        
        with col4:
            st.metric("Pending Registrations", stats.get('pending_registrations', 0))
        
        # Course enrollment chart
        if stats.get('course_stats'):
            st.subheader("📈 Course Enrollment Status")
            
            course_df = pd.DataFrame(stats['course_stats'])
            
            fig = px.bar(
                course_df,
                x='course_code',
                y=['current_enrollment', 'available_spots'],
                title="Course Enrollment vs Available Spots",
                barmode='stack'
            )
            st.plotly_chart(fig, use_container_width=True)
            
            # Enrollment percentage
            fig2 = px.bar(
                course_df,
                x='course_code',
                y='enrollment_percentage',
                title="Course Enrollment Percentage",
                color='enrollment_percentage',
                color_continuous_scale='RdYlGn'
            )
            st.plotly_chart(fig2, use_container_width=True)

def show_students():
    st.header("👥 Students Management")
    
    # Fetch students data
    students = fetch_data("students/")
    
    if students and students.get('results'):
        students_df = pd.DataFrame(students['results'])
        
        # Display metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Students", len(students_df))
        with col2:
            active_students = len(students_df[students_df['is_active'] == True])
            st.metric("Active Students", active_students)
        with col3:
            if 'year_of_study' in students_df.columns:
                most_common_year = students_df['year_of_study'].mode().iloc[0] if not students_df['year_of_study'].mode().empty else "N/A"
                st.metric("Most Common Year", most_common_year)
        
        # Students by year of study
        if 'year_of_study' in students_df.columns:
            year_counts = students_df['year_of_study'].value_counts()
            fig = px.pie(
                values=year_counts.values,
                names=year_counts.index,
                title="Students by Year of Study"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Students table
        st.subheader("Students List")
        display_columns = ['student_id', 'full_name', 'email', 'year_of_study', 'major', 'is_active']
        available_columns = [col for col in display_columns if col in students_df.columns]
        st.dataframe(students_df[available_columns], use_container_width=True)

def show_courses():
    st.header("📚 Courses Management")
    
    # Fetch courses data
    courses = fetch_data("courses/")
    
    if courses and courses.get('results'):
        courses_df = pd.DataFrame(courses['results'])
        
        # Display metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Courses", len(courses_df))
        with col2:
            active_courses = len(courses_df[courses_df['is_active'] == True])
            st.metric("Active Courses", active_courses)
        with col3:
            if 'current_enrollment' in courses_df.columns:
                total_enrollment = courses_df['current_enrollment'].sum()
                st.metric("Total Enrollment", total_enrollment)
        
        # Courses by semester
        if 'semester' in courses_df.columns:
            semester_counts = courses_df['semester'].value_counts()
            fig = px.bar(
                x=semester_counts.index,
                y=semester_counts.values,
                title="Courses by Semester"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Courses table
        st.subheader("Courses List")
        display_columns = ['course_code', 'course_name', 'instructor', 'semester', 'year', 'current_enrollment', 'max_students', 'is_active']
        available_columns = [col for col in display_columns if col in courses_df.columns]
        st.dataframe(courses_df[available_columns], use_container_width=True)

def show_registrations():
    st.header("📝 Registrations Management")
    
    # Fetch registrations data
    registrations = fetch_data("registrations/")
    
    if registrations and registrations.get('results'):
        reg_df = pd.DataFrame(registrations['results'])
        
        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Registrations", len(reg_df))
        with col2:
            enrolled = len(reg_df[reg_df['status'] == 'ENROLLED'])
            st.metric("Enrolled", enrolled)
        with col3:
            pending = len(reg_df[reg_df['status'] == 'PENDING'])
            st.metric("Pending", pending)
        with col4:
            waitlisted = len(reg_df[reg_df['status'] == 'WAITLISTED'])
            st.metric("Waitlisted", waitlisted)
        
        # Registration status distribution
        status_counts = reg_df['status'].value_counts()
        fig = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="Registration Status Distribution"
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Registrations table
        st.subheader("Registrations List")
        display_columns = ['id', 'status', 'registration_date']
        available_columns = [col for col in display_columns if col in reg_df.columns]
        st.dataframe(reg_df[available_columns], use_container_width=True)

def show_analytics():
    st.header("📈 Analytics")
    
    # Fetch trends data
    trends = fetch_data("dashboard/trends/")
    
    if trends:
        # Daily registrations trend
        if trends.get('daily_registrations'):
            daily_df = pd.DataFrame(trends['daily_registrations'])
            if not daily_df.empty:
                daily_df['day'] = pd.to_datetime(daily_df['day'])
                
                fig = px.line(
                    daily_df,
                    x='day',
                    y='count',
                    title="Daily Registration Trends (Last 30 Days)"
                )
                st.plotly_chart(fig, use_container_width=True)
        
        # Status distribution
        if trends.get('status_distribution'):
            status_df = pd.DataFrame(trends['status_distribution'])
            if not status_df.empty:
                fig = px.bar(
                    status_df,
                    x='status',
                    y='count',
                    title="Registration Status Distribution"
                )
                st.plotly_chart(fig, use_container_width=True)

def show_realtime_monitor():
    st.header("📡 Real-time Event Monitor")

    st.info("This page shows real-time database changes captured by Maxwell Daemon")

    # Event statistics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Events Today", "0", "0")
    with col2:
        st.metric("Active Connections", "1", "0")
    with col3:
        st.metric("Last Event", "N/A")

    # Recent events placeholder
    st.subheader("Recent Events")

    # Simulated real-time events (in production, this would connect to Maxwell output)
    events_placeholder = st.empty()

    sample_events = [
        {"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "table": "accounts_student", "type": "insert", "data": {"student_id": "********"}},
        {"timestamp": (datetime.now() - timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M:%S"), "table": "accounts_registration", "type": "update", "data": {"status": "ENROLLED"}},
        {"timestamp": (datetime.now() - timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M:%S"), "table": "accounts_course", "type": "insert", "data": {"course_code": "CS101"}},
    ]

    events_df = pd.DataFrame(sample_events)
    events_placeholder.dataframe(events_df, use_container_width=True)

    # Event type distribution
    st.subheader("Event Type Distribution")
    event_types = ["insert", "update", "delete"]
    event_counts = [5, 12, 2]

    fig = px.pie(values=event_counts, names=event_types, title="Database Operations")
    st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()
