from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views, api_views

# API URL patterns
api_urlpatterns = [
    # Student endpoints
    path('students/', api_views.StudentListCreateView.as_view(), name='student-list-create'),
    path('students/<str:student_id>/', api_views.StudentDetailView.as_view(), name='student-detail'),
    path('students/<str:student_id>/registrations/', api_views.StudentRegistrationsView.as_view(), name='student-registrations'),
    
    # Course endpoints
    path('courses/', api_views.CourseListCreateView.as_view(), name='course-list-create'),
    path('courses/<str:course_code>/', api_views.CourseDetailView.as_view(), name='course-detail'),
    path('courses/<str:course_code>/registrations/', api_views.CourseRegistrationsView.as_view(), name='course-registrations'),
    
    # Registration endpoints
    path('registrations/', api_views.RegistrationListCreateView.as_view(), name='registration-list-create'),
    path('registrations/<int:pk>/', api_views.RegistrationDetailView.as_view(), name='registration-detail'),
    
    # Dashboard endpoints
    path('dashboard/stats/', api_views.dashboard_stats, name='dashboard-stats'),
    path('dashboard/trends/', api_views.registration_trends, name='registration-trends'),
]

# Web URL patterns (existing functionality)
web_urlpatterns = [
    path('register/', views.register, name='register'),
]

urlpatterns = [
    path('api/', include(api_urlpatterns)),
    path('', include(web_urlpatterns)),
]
