2025/07/28 10:43:42 [error] 13996#4872: *1 CreateFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/"
2025/07/28 12:09:09 [emerg] 1424#24780: no "events" section in configuration
2025/07/28 12:09:15 [error] 13996#4872: *4 CreateFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/"
2025/07/28 12:11:05 [emerg] 7240#5040: no "events" section in configuration
2025/07/28 12:12:15 [emerg] 24992#8208: no "events" section in configuration
2025/07/28 12:15:58 [emerg] 22996#3656: no "events" section in configuration
2025/07/28 12:16:07 [emerg] 22728#17344: no "events" section in configuration
2025/07/28 12:16:38 [notice] 10368#16188: signal process started
2025/07/28 12:16:41 [error] 1716#14644: *1 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/"
2025/07/28 12:20:35 [notice] 7772#8468: signal process started
2025/07/28 12:20:46 [error] 21588#15672: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:3000", referrer: "http://localhost:3000/"
2025/07/28 12:21:01 [notice] 26900#18180: signal process started
2025/07/28 12:28:38 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:44 [emerg] 18228#17980: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 12:28:48 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:53 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:54 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:54 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:55 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:55 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:55 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:55 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:56 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:56 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:56 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:28:56 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:20 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:21 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:21 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:21 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:21 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:24 [error] 19808#6240: *19 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:29:36 [emerg] 12684#10104: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 12:29:40 [emerg] 26160#19700: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 12:33:09 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:11 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:11 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:12 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:16 [emerg] 8772#20912: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 12:33:19 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:20 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:21 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:21 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:21 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:22 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 12:33:22 [error] 19808#6240: *22 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:18 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:19 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:19 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:31 [emerg] 21356#23896: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:15:33 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:33 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:33 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:34 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:34 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:15:34 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:21 [emerg] 10580#25620: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:16:29 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:30 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:30 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:34 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:35 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:35 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:35 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:35 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:35 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:36 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:16:36 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:36 [emerg] 11668#17784: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:17:38 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:39 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:39 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:40 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:42 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:42 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:42 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:42 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:43 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:43 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:43 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:43 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:44 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:45 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:45 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:45 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:17:49 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:18:30 [emerg] 16212#12556: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:18:48 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:19:17 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:19:18 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:19:18 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:19:27 [emerg] 5904#23908: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:20:19 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:20:19 [error] 19808#6240: *25 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/movies/"
2025/07/28 13:20:20 [error] 19808#6240: *25 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:22:53 [error] 19808#6240: *32 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:22:53 [error] 19808#6240: *32 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:22:53 [error] 19808#6240: *32 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:22:54 [error] 19808#6240: *32 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:22:54 [error] 19808#6240: *32 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:24:42 [emerg] 20196#12576: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:24:48 [emerg] 6520#19720: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:24:50 [emerg] 3628#14000: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:24:53 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:02 [error] 19808#6240: *35 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/serie" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /serie HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:16 [error] 19808#6240: *35 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/serie" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /serie HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:16 [error] 19808#6240: *35 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/serie" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /serie HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:17 [error] 19808#6240: *35 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/serie" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /serie HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:17 [error] 19808#6240: *35 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/serie" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /serie HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:19 [error] 19808#6240: *35 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:27 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:28 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:28 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:28 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:29 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:29 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:29 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:25:34 [emerg] 20100#4412: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:26:30 [emerg] 6092#18528: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:26:32 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:33 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:33 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:33 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:33 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:34 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:34 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:34 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:34 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:35 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:35 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:41 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:41 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:26:41 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:27:15 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:28 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:31 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:32 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:32 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:32 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:28:33 [error] 19808#6240: *35 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:26 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:30 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:31 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:31 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:31 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:46 [emerg] 28280#20532: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:31:52 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:53 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:53 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:54 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:54 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:31:54 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:32:57 [emerg] 10040#10516: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:33:01 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:02 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:03 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:03 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:04 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:04 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:04 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:05 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:06 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:06 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:06 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:06 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:06 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:07 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:07 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:07 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:07 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:08 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:08 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:09 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:33:09 [error] 19808#6240: *40 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:48:44 [emerg] 27396#8952: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:49:58 [emerg] 10892#17232: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 13:50:03 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:03 [error] 19808#6240: *44 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /favicon.ico HTTP/1.1", host: "localhost:8080", referrer: "http://localhost:8080/movies/"
2025/07/28 13:50:05 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:05 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:05 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:06 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:06 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:06 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:06 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:06 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:07 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:08 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:14 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:14 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:16 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:16 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:16 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:17 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:18 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:19 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:19 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:28 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/" is forbidden, client: 127.0.0.1, server: , request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:35 [error] 19808#6240: *44 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:36 [error] 19808#6240: *44 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:37 [error] 19808#6240: *44 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:37 [error] 19808#6240: *44 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:37 [error] 19808#6240: *44 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 13:50:48 [error] 19808#6240: *44 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:05:31 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:05:32 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:05:32 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:05:33 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:06:59 [error] 19808#6240: *47 "C:/Users/<USER>/Documents/StreamzAI/Practice/serie/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /serie/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:07:32 [emerg] 9916#10740: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 15:08:03 [emerg] 29652#10872: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 15:08:14 [emerg] 17908#18416: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 15:08:14 [emerg] 13272#5516: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 15:08:22 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:08:58 [emerg] 25580#4428: "location" directive is not allowed here in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:9
2025/07/28 15:09:02 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:02 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:02 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:03 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:03 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:03 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:04 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:04 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:04 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:04 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:09:04 [error] 19808#6240: *47 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:10:49 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:10:55 [emerg] 17568#17580: "events" directive is duplicate in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:34
2025/07/28 15:10:59 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:10:59 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:00 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:00 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:00 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:00 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:01 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:01 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:01 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:01 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:01 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:02 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:02 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:02 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:02 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:02 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:03 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:03 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:03 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:03 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:10 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:10 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:10 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:11 [error] 19808#6240: *51 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:13 [notice] 23792#16472: signal process started
2025/07/28 15:11:15 [error] 19808#6240: *52 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:17 [error] 19808#6240: *53 directory index of "C:/Users/<USER>/Documents/StreamzAI/Practice/series/" is forbidden, client: 127.0.0.1, server: , request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:11:48 [notice] 24572#11376: signal process started
2025/07/28 15:12:44 [notice] 11928#15516: signal process started
2025/07/28 15:12:49 [notice] 11112#23464: signal process started
2025/07/28 15:14:16 [error] 13996#4872: *18 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:14:16 [error] 13996#4872: *18 CreateFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/movies/"
2025/07/28 15:14:34 [error] 13996#4872: *18 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:17:37 [emerg] 28292#16568: invalid number of arguments in "try_files" directive in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:22
2025/07/28 15:17:47 [error] 13996#4872: *21 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:17:48 [error] 13996#4872: *21 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:17:48 [error] 13996#4872: *21 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:17:48 [error] 13996#4872: *21 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:17:59 [emerg] 25608#17588: invalid number of arguments in "try_files" directive in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:22
2025/07/28 15:18:11 [emerg] 23564#15812: invalid number of arguments in "try_files" directive in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:22
2025/07/28 15:18:18 [emerg] 10132#19060: invalid number of arguments in "try_files" directive in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:22
2025/07/28 15:21:33 [notice] 26600#4496: signal process started
2025/07/28 15:21:41 [error] 13996#4872: *24 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:21:43 [error] 13996#4872: *24 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:21:51 [error] 13996#4872: *24 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:22:00 [notice] 8280#22132: signal process started
2025/07/28 15:22:54 [notice] 9720#15140: signal process started
2025/07/28 15:22:57 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:22:58 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:22:59 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:00 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:00 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:00 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:00 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:05 [notice] 17888#27800: signal process started
2025/07/28 15:23:11 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:23:14 [error] 13996#4872: *27 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:22 [notice] 15092#3552: signal process started
2025/07/28 15:24:24 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:26 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:26 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:26 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:26 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:26 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:31 [notice] 19956#13928: signal process started
2025/07/28 15:24:35 [notice] 6884#3228: signal process started
2025/07/28 15:24:39 [notice] 10740#19152: signal process started
2025/07/28 15:24:42 [notice] 6404#27220: signal process started
2025/07/28 15:24:42 [error] 6404#27220: CreateFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/07/28 15:24:48 [notice] 14164#3028: signal process started
2025/07/28 15:24:56 [notice] 8720#3512: signal process started
2025/07/28 15:24:58 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:24:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:48 [notice] 27164#14308: signal process started
2025/07/28 15:25:51 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:57 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:57 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:58 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:58 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:58 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:58 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:25:59 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:26:05 [error] 13996#4872: *30 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:27:16 [error] 13996#4872: *34 CreateFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/html/series" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series HTTP/1.1", host: "localhost"
2025/07/28 15:29:32 [notice] 6220#4436: signal process started
2025/07/28 15:29:42 [error] 13996#4872: *37 "C:\Users\<USER>\Downloads\nginx-1.28.0/html/movies/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost"
2025/07/28 15:31:42 [notice] 24948#23404: signal process started
2025/07/28 15:35:04 [emerg] 6804#12808: "events" directive is duplicate in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:33
2025/07/28 15:35:40 [emerg] 11336#16324: "events" directive is duplicate in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:31
2025/07/28 15:35:55 [notice] 9176#26576: signal process started
2025/07/28 15:36:04 [error] 22720#26420: *1 "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/movies/movies.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:36:04 [error] 22720#26420: *1 "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/movies/movies.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:36:20 [notice] 17060#24364: signal process started
2025/07/28 15:36:22 [error] 22720#26420: *2 "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/movies/movies.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:36:23 [error] 22720#26420: *3 "C:/Users/<USER>/Documents/StreamzAI/Practice/movies/movies/movies.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: localhost, request: "GET /movies/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:36:48 [notice] 18392#21884: signal process started
2025/07/28 15:37:15 [emerg] 22160#10016: invalid number of arguments in "try_files" directive in C:\Users\<USER>\Downloads\nginx-1.28.0/conf/nginx.conf:26
2025/07/28 15:37:27 [error] 12072#7624: *6 FindFirstFile() "C:/Users/<USER>/Documents/StreamzAI/Practic" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:15 [notice] 10492#24648: signal process started
2025/07/28 15:38:35 [error] 4792#19148: *8 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:47 [notice] 7336#16104: signal process started
2025/07/28 15:38:48 [error] 4792#19148: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:49 [error] 2296#14448: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:50 [error] 2296#14448: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:50 [error] 2296#14448: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:50 [error] 2296#14448: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:38:57 [error] 2296#14448: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:39:00 [notice] 26652#13176: signal process started
2025/07/28 15:39:01 [error] 2296#14448: *11 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:39:01 [error] 28352#26836: *12 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:39:01 [error] 28352#26836: *12 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:39:02 [error] 28352#26836: *12 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:39:43 [notice] 13072#28300: signal process started
2025/07/28 15:39:45 [error] 28352#26836: *13 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:41:28 [notice] 5744#22708: signal process started
2025/07/28 15:41:43 [notice] 24488#2340: signal process started
2025/07/28 15:41:46 [notice] 29560#10912: signal process started
2025/07/28 15:41:50 [notice] 21900#28532: signal process started
2025/07/28 15:41:51 [alert] 7548#19952: DeleteFile() "C:\Users\<USER>\Downloads\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/07/28 15:41:53 [notice] 7232#5052: signal process started
2025/07/28 15:47:17 [notice] 9340#6220: signal process started
2025/07/28 15:47:25 [error] 4684#28856: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:47:26 [error] 4684#28856: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:47:43 [error] 4684#28856: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:47:46 [error] 4684#28856: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:47:47 [error] 4684#28856: *9 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:47:52 [notice] 19568#24452: signal process started
2025/07/28 15:47:54 [error] 4684#28856: *10 CreateFile() "C:/Users/<USER>/Documents/StreamzAI/Practice404" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /series/ HTTP/1.1", host: "localhost:8080"
2025/07/28 15:49:04 [notice] 17884#2384: signal process started
2025/07/28 16:20:43 [notice] 8308#25008: signal process started
2025/07/29 11:16:02 [notice] 26136#12700: signal process started
2025/07/29 12:21:01 [notice] 10356#12520: signal process started
2025/07/29 12:22:09 [error] 1600#2652: *10 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: , request: "GET /django/ HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "localhost"
2025/07/29 12:22:26 [notice] 2904#3136: signal process started
2025/07/29 12:22:32 [notice] 25908#20148: signal process started
2025/07/29 12:22:37 [error] 1600#2652: *10 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: , request: "GET /django/ HTTP/1.1", upstream: "http://127.0.0.1:8000/", host: "localhost"
2025/07/29 12:23:05 [error] 1600#2652: *10 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:25:56 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:00 [notice] 23264#15292: signal process started
2025/07/29 12:26:02 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:03 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:03 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:03 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:03 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:03 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:04 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:04 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:26:17 [error] 1600#2652: *16 "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /register/ HTTP/1.1", host: "localhost"
2025/07/29 12:26:32 [error] 1600#2652: *16 "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /register/ HTTP/1.1", host: "localhost"
2025/07/29 12:27:37 [error] 1600#2652: *16 "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/success/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /success/ HTTP/1.1", host: "localhost", referrer: "http://localhost/django/register/"
2025/07/29 12:28:15 [notice] 27144#27572: signal process started
2025/07/29 12:28:33 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:35 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:36 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:45 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:46 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:46 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:28:46 [error] 1600#2652: *16 CreateFile() "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/register" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: , request: "GET /register HTTP/1.1", host: "localhost"
2025/07/29 12:29:10 [notice] 25168#26760: signal process started
2025/07/29 12:29:16 [notice] 24760#23784: signal process started
2025/07/29 12:30:29 [error] 1600#2652: *16 "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/success/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /success/ HTTP/1.1", host: "localhost", referrer: "http://localhost/django/register/"
2025/07/29 12:37:33 [error] 1600#2652: *40 "C:\Users\<USER>\Documents\StreamzAI\Practice\Django_app\nginx-1.28.0/html/success/index.html" is not found (3: The system cannot find the path specified), client: 127.0.0.1, server: , request: "GET /success/ HTTP/1.1", host: "localhost", referrer: "http://localhost/django/register/"
