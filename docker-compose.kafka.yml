# Optional Kafka integration for <PERSON>
# Use with: docker-compose -f docker-compose.yml -f docker-compose.kafka.yml up

version: '3.8'

services:
  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: student_registration_zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - student_network

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: student_registration_kafka
    restart: unless-stopped
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - student_network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Maxwell with Kafka output
  maxwell:
    command: >
      bin/maxwell
      --config=/app/config-kafka.properties
    volumes:
      - ./maxwell/config-kafka.properties:/app/config-kafka.properties
    depends_on:
      kafka:
        condition: service_healthy

  # Kafka Consumer for monitoring
  kafka-consumer:
    image: confluentinc/cp-kafka:7.4.0
    container_name: student_registration_kafka_consumer
    restart: unless-stopped
    depends_on:
      - kafka
    command: >
      kafka-console-consumer
      --bootstrap-server kafka:9092
      --topic student_registration_events
      --from-beginning
    networks:
      - student_network
