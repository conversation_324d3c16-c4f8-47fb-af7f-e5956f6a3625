#!/bin/bash

# Student Registration System Deployment Script
# This script automates the deployment process

set -e

echo "🎓 Student Registration System Deployment"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available ports
    if netstat -tuln | grep -q ":80 "; then
        print_warning "Port 80 is already in use. You may need to stop other services."
    fi
    
    if netstat -tuln | grep -q ":3306 "; then
        print_warning "Port 3306 is already in use. You may need to stop other MySQL services."
    fi
    
    print_success "Prerequisites check completed"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        print_status "Creating .env file from template..."
        cp .env .env.backup 2>/dev/null || true
        
        # Generate random secret key
        SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(50))")
        
        # Create .env file
        cat > .env << EOF
# Django Configuration
SECRET_KEY=${SECRET_KEY}
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DB_NAME=student_registration
DB_USER=root
DB_PASSWORD=rootpassword123
DB_HOST=mysql
DB_PORT=3306

# Django Superuser
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=admin123

# MySQL Configuration
MYSQL_ROOT_PASSWORD=rootpassword123
MYSQL_DATABASE=student_registration
MYSQL_USER=django_user
MYSQL_PASSWORD=django_password123

# Maxwell Configuration
MAXWELL_USER=maxwell
MAXWELL_PASSWORD=maxwell_password123
EOF
        
        print_success "Environment file created"
    else
        print_status "Environment file already exists"
    fi
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Pull latest images
    print_status "Pulling Docker images..."
    docker-compose pull
    
    # Build custom images
    print_status "Building custom images..."
    docker-compose build
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    print_success "Services started"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for MySQL
    print_status "Waiting for MySQL..."
    timeout=60
    while ! docker-compose exec -T mysql mysqladmin ping -h localhost --silent; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "MySQL failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "MySQL is ready"
    
    # Wait for Django
    print_status "Waiting for Django..."
    timeout=60
    while ! curl -f http://localhost:8000/admin/ &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Django failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Django is ready"
    
    # Wait for Streamlit
    print_status "Waiting for Streamlit..."
    timeout=60
    while ! curl -f http://localhost:8501/_stcore/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Streamlit failed to start within 60 seconds"
            exit 1
        fi
    done
    print_success "Streamlit is ready"
    
    # Wait for Nginx
    print_status "Waiting for Nginx..."
    timeout=30
    while ! curl -f http://localhost/health &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "Nginx failed to start within 30 seconds"
            exit 1
        fi
    done
    print_success "Nginx is ready"
}

# Load sample data
load_sample_data() {
    print_status "Loading sample data..."
    
    # Create sample data script
    cat > load_sample_data.py << 'EOF'
from django.contrib.auth.models import User
from accounts.models import Student, Course, Registration
from datetime import date

# Create sample courses
courses_data = [
    {"course_code": "CS101", "course_name": "Introduction to Computer Science", "instructor": "Dr. Smith", "semester": "FALL", "year": 2024, "credits": 3, "max_students": 30},
    {"course_code": "MATH201", "course_name": "Calculus II", "instructor": "Prof. Johnson", "semester": "FALL", "year": 2024, "credits": 4, "max_students": 25},
    {"course_code": "ENG102", "course_name": "English Composition", "instructor": "Dr. Brown", "semester": "SPRING", "year": 2024, "credits": 3, "max_students": 20},
]

for course_data in courses_data:
    course, created = Course.objects.get_or_create(
        course_code=course_data["course_code"],
        defaults=course_data
    )
    if created:
        print(f"Created course: {course.course_code}")

# Create sample students
students_data = [
    {"username": "john.doe", "email": "<EMAIL>", "first_name": "John", "last_name": "Doe", "student_id": "12345678", "year_of_study": "SOPHOMORE"},
    {"username": "jane.smith", "email": "<EMAIL>", "first_name": "Jane", "last_name": "Smith", "student_id": "12345679", "year_of_study": "FRESHMAN"},
    {"username": "bob.wilson", "email": "<EMAIL>", "first_name": "Bob", "last_name": "Wilson", "student_id": "12345680", "year_of_study": "JUNIOR"},
]

for student_data in students_data:
    user, created = User.objects.get_or_create(
        username=student_data["username"],
        defaults={
            "email": student_data["email"],
            "first_name": student_data["first_name"],
            "last_name": student_data["last_name"],
        }
    )
    if created:
        user.set_password("password123")
        user.save()
        
        student = Student.objects.create(
            user=user,
            student_id=student_data["student_id"],
            phone_number="+1234567890",
            date_of_birth=date(2000, 1, 1),
            address="123 Main St, City, State",
            emergency_contact_name="Emergency Contact",
            emergency_contact_phone="+1234567891",
            year_of_study=student_data["year_of_study"],
            major="Computer Science"
        )
        print(f"Created student: {student.student_id}")

print("Sample data loaded successfully!")
EOF
    
    docker-compose exec -T django python manage.py shell < load_sample_data.py
    rm load_sample_data.py
    
    print_success "Sample data loaded"
}

# Display access information
show_access_info() {
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo "===================================="
    echo ""
    echo "Access your Student Registration System:"
    echo ""
    echo "📊 Main Dashboard:     http://localhost/dashboard/"
    echo "🔧 Django Admin:       http://localhost/admin/"
    echo "📡 API Endpoints:      http://localhost/accounts/api/"
    echo "📈 Streamlit Direct:   http://localhost:8501/"
    echo ""
    echo "Default Admin Credentials:"
    echo "Username: admin"
    echo "Password: admin123"
    echo ""
    echo "Useful Commands:"
    echo "• View logs:           docker-compose logs -f"
    echo "• Stop services:       docker-compose down"
    echo "• Restart services:    docker-compose restart"
    echo "• Update services:     docker-compose pull && docker-compose up -d"
    echo ""
}

# Main deployment process
main() {
    echo "Starting deployment process..."
    
    check_prerequisites
    setup_environment
    deploy_services
    wait_for_services
    load_sample_data
    show_access_info
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        print_status "Stopping services..."
        docker-compose down
        print_success "Services stopped"
        ;;
    "restart")
        print_status "Restarting services..."
        docker-compose restart
        print_success "Services restarted"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the complete system (default)"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  logs     - Show service logs"
        echo "  status   - Show service status"
        exit 1
        ;;
esac
