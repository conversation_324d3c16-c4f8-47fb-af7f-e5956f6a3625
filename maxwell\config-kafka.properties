# Maxwell Configuration for Kafka Output
host=mysql
port=3306
user=maxwell
password=maxwell_password

# Database to replicate
database=student_registration

# Kafka producer configuration
producer=kafka
kafka.bootstrap.servers=kafka:9092
kafka_topic=student_registration_events

# Include specific tables for student registration events
include_tables=accounts_student,accounts_course,accounts_registration

# Output format
output_format=json

# Include additional metadata
output_ddl=true
output_binlog_position=true
output_gtid_position=true
output_commit_info=true
output_xoffset=true

# Binlog position
binlog_connector_max_wait=10000

# Metrics
metrics_type=slf4j
metrics_slf4j_interval=60

# Schema database
schema_database=maxwell

# Filtering
filter=exclude: *.*, include: accounts_student.*, include: accounts_course.*, include: accounts_registration.*

# Row query
include_column_values=true
