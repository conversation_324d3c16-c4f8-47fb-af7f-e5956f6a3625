# 📁 Project Structure

This document outlines the complete structure of the Student Registration System project.

```
student-registration-system/
├── 📄 README.md                          # Main documentation
├── 📄 PROJECT_STRUCTURE.md               # This file
├── 📄 requirements.txt                   # Python dependencies
├── 📄 .env                              # Environment variables
├── 📄 docker-compose.yml                # Main Docker Compose configuration
├── 📄 docker-compose.kafka.yml          # Optional Kafka integration
├── 📄 Dockerfile                        # Django application Dockerfile
├── 📄 docker-entrypoint.sh              # Django container entrypoint
├── 📄 deploy.sh                         # Deployment automation script
├── 📄 test_system.py                    # System testing script
│
├── 📁 mysite/                           # Django project root
│   ├── 📄 manage.py                     # Django management script
│   ├── 📄 db.sqlite3                    # Development SQLite database
│   │
│   ├── 📁 mysite/                       # Django project settings
│   │   ├── 📄 __init__.py
│   │   ├── 📄 settings.py               # Django configuration
│   │   ├── 📄 urls.py                   # Main URL routing
│   │   ├── 📄 wsgi.py                   # WSGI configuration
│   │   └── 📄 asgi.py                   # ASGI configuration
│   │
│   ├── 📁 accounts/                     # Student registration app
│   │   ├── 📄 __init__.py
│   │   ├── 📄 admin.py                  # Django admin configuration
│   │   ├── 📄 apps.py                   # App configuration
│   │   ├── 📄 models.py                 # Database models (Student, Course, Registration)
│   │   ├── 📄 serializers.py            # DRF serializers
│   │   ├── 📄 views.py                  # Web views (existing functionality)
│   │   ├── 📄 api_views.py              # REST API views
│   │   ├── 📄 urls.py                   # App URL routing
│   │   ├── 📄 forms.py                  # Django forms
│   │   ├── 📄 tests.py                  # Unit tests
│   │   └── 📁 migrations/               # Database migrations
│   │
│   ├── 📁 hello/                        # Original hello app
│   │   ├── 📄 __init__.py
│   │   ├── 📄 admin.py
│   │   ├── 📄 apps.py
│   │   ├── 📄 models.py
│   │   ├── 📄 views.py
│   │   ├── 📄 tests.py
│   │   └── 📁 migrations/
│   │
│   └── 📁 templates/                    # HTML templates
│       ├── 📄 register.html             # Registration form
│       └── 📄 success.html              # Success page
│
├── 📁 streamlit_dashboard/              # Streamlit analytics dashboard
│   ├── 📄 Dockerfile                    # Streamlit container
│   ├── 📄 requirements.txt              # Streamlit dependencies
│   ├── 📄 app.py                        # Main Streamlit application
│   └── 📁 .streamlit/                   # Streamlit configuration
│       └── 📄 config.toml               # Streamlit settings
│
├── 📁 nginx/                            # Nginx reverse proxy configuration
│   ├── 📄 nginx.conf                    # Main Nginx configuration
│   ├── 📁 conf.d/                       # Server configurations
│   │   ├── 📄 default.conf              # Main server block
│   │   └── 📄 ssl.conf.example          # HTTPS configuration template
│   └── 📁 ssl/                          # SSL certificates
│       └── 📄 generate-ssl.sh           # SSL certificate generation script
│
├── 📁 mysql/                            # MySQL database configuration
│   └── 📄 init.sql                      # Database initialization script
│
└── 📁 maxwell/                          # Maxwell Daemon configuration
    ├── 📄 config.properties              # Maxwell configuration (stdout)
    ├── 📄 config-kafka.properties        # Maxwell configuration (Kafka)
    └── 📄 monitor.py                     # Maxwell output monitoring script
```

## 🔧 Key Components

### Django Application (`mysite/`)

The core Django application containing:

- **Models**: Student, Course, Registration entities with relationships
- **API Views**: RESTful endpoints for all operations
- **Serializers**: Data transformation for API responses
- **Admin Interface**: Django admin for system management
- **Web Views**: Traditional Django views for existing functionality

### Streamlit Dashboard (`streamlit_dashboard/`)

Interactive analytics dashboard featuring:

- **Real-time Monitoring**: Live data updates and event tracking
- **Data Visualization**: Charts and graphs for enrollment analytics
- **Management Interface**: Quick actions for common operations
- **Responsive Design**: Mobile-friendly interface

### Nginx Configuration (`nginx/`)

Reverse proxy and web server setup:

- **Load Balancing**: Traffic distribution across services
- **Static File Serving**: Optimized delivery of static assets
- **SSL Termination**: HTTPS support with configurable certificates
- **Rate Limiting**: API protection and security

### Database Setup (`mysql/`)

MySQL database configuration:

- **Binary Logging**: Enabled for real-time streaming
- **User Management**: Separate users for different services
- **Initialization**: Automated setup scripts
- **Performance Tuning**: Optimized for read/write operations

### Maxwell Integration (`maxwell/`)

Real-time data streaming:

- **Binlog Monitoring**: Captures all database changes
- **Multiple Outputs**: Support for stdout and Kafka
- **Event Filtering**: Configurable table and operation filtering
- **Monitoring Tools**: Scripts for event tracking and analysis

## 🚀 Deployment Files

### Docker Configuration

- **`Dockerfile`**: Django application container
- **`docker-compose.yml`**: Multi-service orchestration
- **`docker-compose.kafka.yml`**: Optional Kafka integration
- **`docker-entrypoint.sh`**: Django container initialization

### Automation Scripts

- **`deploy.sh`**: Complete deployment automation
- **`test_system.py`**: Comprehensive system testing
- **Environment Files**: Configuration management

## 📊 Data Flow

```
User Request → Nginx → Django API → MySQL Database
                ↓         ↓            ↓
            Streamlit ← Dashboard ← Maxwell Daemon
```

1. **User Requests**: Routed through Nginx reverse proxy
2. **API Processing**: Django handles business logic and data operations
3. **Database Operations**: MySQL stores and retrieves data
4. **Real-time Streaming**: Maxwell captures database changes
5. **Analytics**: Streamlit displays real-time analytics and monitoring

## 🔒 Security Considerations

### Environment Variables
- All sensitive data stored in `.env` file
- Production secrets should be managed securely
- Database passwords and API keys properly isolated

### Network Security
- Services communicate through internal Docker network
- Only necessary ports exposed to host
- Rate limiting configured for public endpoints

### Data Protection
- Input validation on all API endpoints
- SQL injection protection through Django ORM
- CORS configuration for cross-origin requests

## 📈 Scalability

### Horizontal Scaling
- **Django**: Multiple application instances behind load balancer
- **Database**: MySQL replication and clustering support
- **Analytics**: Distributed Streamlit instances
- **Proxy**: Nginx upstream configuration for load balancing

### Performance Optimization
- **Caching**: Redis integration for session and query caching
- **CDN**: Static file delivery through content delivery network
- **Database**: Proper indexing and query optimization
- **Monitoring**: Application performance monitoring integration

## 🧪 Testing Strategy

### Unit Tests
- Django model and view testing
- API endpoint validation
- Business logic verification

### Integration Tests
- Service communication testing
- Database operation validation
- End-to-end workflow testing

### Performance Tests
- Load testing for API endpoints
- Database performance benchmarking
- Real-time streaming validation

## 📚 Documentation

### API Documentation
- RESTful endpoint specifications
- Request/response examples
- Authentication and authorization

### Deployment Documentation
- Step-by-step setup instructions
- Configuration options
- Troubleshooting guides

### User Documentation
- Dashboard usage instructions
- Administrative procedures
- Common workflows
