# Docker Compose configuration for Student Registration System

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: student_registration_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    command: >
      --server-id=1
      --log-bin=mysql-bin
      --binlog-format=ROW
      --binlog-do-db=${MYSQL_DATABASE}
      --gtid-mode=ON
      --enforce-gtid-consistency=ON
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - student_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Django Application
  django:
    build: .
    container_name: student_registration_django
    restart: unless-stopped
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DJANGO_SUPERUSER_USERNAME=${DJANGO_SUPERUSER_USERNAME}
      - DJANGO_SUPERUSER_EMAIL=${DJANGO_SUPERUSER_EMAIL}
      - DJANGO_SUPERUSER_PASSWORD=${DJANGO_SUPERUSER_PASSWORD}
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - student_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Maxwell Daemon for MySQL binlog streaming (optional - uncomment to enable)
  # maxwell:
  #   image: zendesk/maxwell:1.40.1
  #   container_name: student_registration_maxwell
  #   restart: unless-stopped
  #   environment:
  #     - MAXWELL_HOST=mysql
  #     - MAXWELL_PORT=3306
  #     - MAXWELL_USER=${MAXWELL_USER}
  #     - MAXWELL_PASSWORD=${MAXWELL_PASSWORD}
  #     - MAXWELL_DATABASE=${MYSQL_DATABASE}
  #   command: >
  #     bin/maxwell
  #     --host=mysql
  #     --port=3306
  #     --user=${MAXWELL_USER}
  #     --password=${MAXWELL_PASSWORD}
  #     --producer=stdout
  #     --output_ddl=true
  #     --databases=${MYSQL_DATABASE}
  #   depends_on:
  #     mysql:
  #       condition: service_healthy
  #   networks:
  #     - student_network
  #   volumes:
  #     - ./maxwell/config.properties:/app/config.properties

  # Streamlit Dashboard
  streamlit:
    build: ./streamlit_dashboard
    container_name: student_registration_streamlit
    restart: unless-stopped
    ports:
      - "8501:8501"
    depends_on:
      - django
    networks:
      - student_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: student_registration_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - django
      - streamlit
    networks:
      - student_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  static_volume:
  media_volume:

networks:
  student_network:
    driver: bridge
