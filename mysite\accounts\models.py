from django.db import models
from django.contrib.auth.models import User
from django.core.validators import EmailValidator, RegexValidator
from django.utils import timezone


class Course(models.Model):
    """Model representing a course that students can register for"""
    course_code = models.CharField(
        max_length=10,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[A-Z]{2,4}\d{3,4}$',
            message='Course code must be in format like CS101 or MATH1001'
        )]
    )
    course_name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    credits = models.PositiveIntegerField(default=3)
    max_students = models.PositiveIntegerField(default=30)
    instructor = models.CharField(max_length=100)
    semester = models.CharField(
        max_length=20,
        choices=[
            ('FALL', 'Fall'),
            ('SPRING', 'Spring'),
            ('SUMMER', 'Summer'),
        ]
    )
    year = models.PositiveIntegerField(default=timezone.now().year)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['course_code']
        unique_together = ['course_code', 'semester', 'year']

    def __str__(self):
        return f"{self.course_code} - {self.course_name}"

    @property
    def current_enrollment(self):
        return self.registrations.filter(status='ENROLLED').count()

    @property
    def available_spots(self):
        return self.max_students - self.current_enrollment


class Student(models.Model):
    """Model representing a student"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    student_id = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(
            regex=r'^\d{8,12}$',
            message='Student ID must be 8-12 digits'
        )]
    )
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.'
        )]
    )
    date_of_birth = models.DateField()
    address = models.TextField()
    emergency_contact_name = models.CharField(max_length=100)
    emergency_contact_phone = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.'
        )]
    )
    major = models.CharField(max_length=100, blank=True)
    year_of_study = models.CharField(
        max_length=20,
        choices=[
            ('FRESHMAN', 'Freshman'),
            ('SOPHOMORE', 'Sophomore'),
            ('JUNIOR', 'Junior'),
            ('SENIOR', 'Senior'),
            ('GRADUATE', 'Graduate'),
        ]
    )
    gpa = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['student_id']

    def __str__(self):
        return f"{self.student_id} - {self.user.get_full_name()}"

    @property
    def full_name(self):
        return self.user.get_full_name()

    @property
    def email(self):
        return self.user.email


class Registration(models.Model):
    """Model representing a student's registration for a course"""
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('ENROLLED', 'Enrolled'),
        ('WAITLISTED', 'Waitlisted'),
        ('DROPPED', 'Dropped'),
        ('COMPLETED', 'Completed'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='registrations')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='registrations')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    registration_date = models.DateTimeField(auto_now_add=True)
    grade = models.CharField(
        max_length=2,
        blank=True,
        null=True,
        choices=[
            ('A+', 'A+'), ('A', 'A'), ('A-', 'A-'),
            ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'),
            ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'),
            ('D+', 'D+'), ('D', 'D'), ('F', 'F'),
            ('I', 'Incomplete'), ('W', 'Withdrawn'),
        ]
    )
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['student', 'course']
        ordering = ['-registration_date']

    def __str__(self):
        return f"{self.student.student_id} - {self.course.course_code} ({self.status})"

    def save(self, *args, **kwargs):
        # Auto-enroll if spots available, otherwise waitlist
        if self.status == 'PENDING':
            if self.course.available_spots > 0:
                self.status = 'ENROLLED'
            else:
                self.status = 'WAITLISTED'
        super().save(*args, **kwargs)
